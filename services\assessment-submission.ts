/**
 * Assessment submission service with loading page integration
 */

import { calculateAllScores } from '../utils/assessment-calculations';
import { validateAnswers } from '../utils/assessment-validation';
import { submitAssessment as submitEnhancedAssessment } from './enhanced-assessment-api';
import { submitAssessment as submitMockAssessment } from './assessment-api';
import { isAssessmentServiceAvailable } from './enhanced-assessment-api';

export interface AssessmentSubmissionResult {
  jobId?: string;
  resultId?: string;
  status: 'processing' | 'completed';
  personaTitle?: string;
  useLoadingPage: boolean;
}

/**
 * Submit assessment with automatic routing to loading page or results
 */
export async function submitAssessmentWithLoading(
  answers: Record<number, number | null>,
  userId?: string,
  onTokenBalanceUpdate?: () => Promise<void>
): Promise<AssessmentSubmissionResult> {
  
  console.log('AssessmentSubmission: Starting submission with loading page integration');
  
  // Validate answers
  const validation = validateAnswers(answers);
  if (!validation.isValid) {
    throw new Error(`Missing ${validation.missingQuestions.length} answers. Please complete all questions.`);
  }

  // Calculate scores
  const scores = calculateAllScores(answers);
  
  try {
    // Check if enhanced API is available
    const serviceAvailable = await isAssessmentServiceAvailable();
    
    if (serviceAvailable) {
      console.log('AssessmentSubmission: Using enhanced API with loading page');
      
      // Use enhanced API - returns jobId for polling
      const submitResponse = await submitEnhancedAssessment(
        scores,
        'AI-Driven Talent Mapping',
        onTokenBalanceUpdate
      );
      
      return {
        jobId: submitResponse.data.jobId,
        status: 'processing',
        useLoadingPage: true
      };
      
    } else {
      console.log('AssessmentSubmission: Using mock API, skipping loading page');
      
      // Use mock API - returns resultId directly
      const mockResponse = await submitMockAssessment(
        answers,
        userId,
        onTokenBalanceUpdate
      );
      
      return {
        resultId: mockResponse.resultId,
        status: 'completed',
        personaTitle: mockResponse.personaTitle,
        useLoadingPage: false
      };
    }
    
  } catch (error) {
    console.error('AssessmentSubmission: Enhanced API failed, falling back to mock API');
    
    // Fallback to mock API
    const mockResponse = await submitMockAssessment(
      answers,
      userId,
      onTokenBalanceUpdate
    );
    
    return {
      resultId: mockResponse.resultId,
      status: 'completed',
      personaTitle: mockResponse.personaTitle,
      useLoadingPage: false
    };
  }
}

/**
 * Submit assessment with flexible validation (allows partial completion)
 */
export async function submitAssessmentFlexibleWithLoading(
  answers: Record<number, number | null>,
  userId?: string,
  onTokenBalanceUpdate?: () => Promise<void>
): Promise<AssessmentSubmissionResult> {
  
  console.log('AssessmentSubmission: Starting flexible submission with loading page integration');
  
  // Check if we have enough answers to generate meaningful results
  const validation = validateAnswers(answers);
  const completionRate = validation.answeredQuestions / validation.totalQuestions;

  if (completionRate < 0.5) {
    throw new Error(`Insufficient answers. Please complete at least 50% of questions (${Math.ceil(validation.totalQuestions * 0.5)} questions).`);
  }

  // Calculate scores with available answers
  const scores = calculateAllScores(answers);
  
  try {
    // Check if enhanced API is available
    const serviceAvailable = await isAssessmentServiceAvailable();
    
    if (serviceAvailable) {
      console.log('AssessmentSubmission: Using enhanced API with loading page (flexible)');
      
      // Use enhanced API - returns jobId for polling
      const submitResponse = await submitEnhancedAssessment(
        scores,
        'AI-Driven Talent Mapping (Partial)',
        onTokenBalanceUpdate
      );
      
      return {
        jobId: submitResponse.data.jobId,
        status: 'processing',
        useLoadingPage: true
      };
      
    } else {
      console.log('AssessmentSubmission: Using mock API, skipping loading page (flexible)');
      
      // Use mock API - returns resultId directly
      const { submitAssessmentFlexible } = await import('./assessment-api');
      const mockResponse = await submitAssessmentFlexible(
        answers,
        userId,
        onTokenBalanceUpdate
      );
      
      return {
        resultId: mockResponse.resultId,
        status: 'completed',
        personaTitle: mockResponse.personaTitle,
        useLoadingPage: false
      };
    }
    
  } catch (error) {
    console.error('AssessmentSubmission: Enhanced API failed, falling back to mock API (flexible)');
    
    // Fallback to mock API
    const { submitAssessmentFlexible } = await import('./assessment-api');
    const mockResponse = await submitAssessmentFlexible(
      answers,
      userId,
      onTokenBalanceUpdate
    );
    
    return {
      resultId: mockResponse.resultId,
      status: 'completed',
      personaTitle: mockResponse.personaTitle,
      useLoadingPage: false
    };
  }
}
