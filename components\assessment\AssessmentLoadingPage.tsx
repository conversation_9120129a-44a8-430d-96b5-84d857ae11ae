'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { 
  Clock, 
  Users, 
  Activity, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  RefreshCw,
  Loader2,
  ArrowLeft
} from 'lucide-react';
import { getAssessmentStatus, getAssessmentQueueInfo } from '../../services/enhanced-assessment-api';
import { formatTimeRemaining } from '../../services/enhanced-assessment-api';
import LottieAnimation from '../ui/LottieAnimation';

interface AssessmentLoadingPageProps {
  jobId: string;
  onComplete?: (resultId: string) => void;
  onError?: (error: string) => void;
}

interface QueueInfo {
  queueLength: number;
  estimatedWaitTime: string;
  averageProcessingTime: string;
  isServiceAvailable: boolean;
}

export default function AssessmentLoadingPage({
  jobId,
  onComplete,
  onError
}: AssessmentLoadingPageProps) {
  const router = useRouter();
  const [status, setStatus] = useState<'queued' | 'processing' | 'completed' | 'failed'>('queued');
  const [progress, setProgress] = useState(0);
  const [message, setMessage] = useState('Memulai proses analisis...');
  const [queuePosition, setQueuePosition] = useState<number | null>(null);
  const [estimatedTime, setEstimatedTime] = useState<string | null>(null);
  const [queueInfo, setQueueInfo] = useState<QueueInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Polling untuk status assessment
  useEffect(() => {
    if (!jobId || !isPolling) return;

    const pollStatus = async () => {
      try {
        // Handle demo scenarios
        if (jobId.startsWith('demo-job')) {
          const mockData = await getMockAssessmentStatus(jobId, elapsedTime);
          handleStatusUpdate(mockData);
          return;
        }

        const statusResponse = await getAssessmentStatus(jobId);
        const data = statusResponse.data;
        handleStatusUpdate(data);
      } catch (err) {
        console.error('Error polling assessment status:', err);
        setError('Gagal mengecek status assessment');
        setIsPolling(false);
        if (onError) {
          onError('Failed to check assessment status');
        }
      }
    };

    const handleStatusUpdate = (data: any) => {
      setStatus(data.status);
      setProgress(data.progress || 0);
      setQueuePosition(data.queuePosition);
      setEstimatedTime(data.estimatedTimeRemaining ? formatTimeRemaining(data.estimatedTimeRemaining) : null);

      // Update message berdasarkan status
      switch (data.status) {
        case 'queued':
          setMessage(`Menunggu dalam antrian... ${data.queuePosition ? `Posisi: ${data.queuePosition}` : ''}`);
          break;
        case 'processing':
          setMessage('Sedang menganalisis hasil assessment Anda...');
          break;
        case 'completed':
          setMessage('Analisis selesai! Mengarahkan ke hasil...');
          setIsPolling(false);
          if (onComplete && data.resultId) {
            // Save result to localStorage if available
            if (data.result) {
              localStorage.setItem(`assessment-result-${data.resultId}`, JSON.stringify(data.result));
              console.log('AssessmentLoadingPage: Saved result to localStorage:', data.resultId);
            }
            setTimeout(() => onComplete(data.resultId), 1500);
          }
          break;
        case 'failed':
          setMessage('Terjadi kesalahan dalam proses analisis');
          setError(data.error || 'Unknown error occurred');
          setIsPolling(false);
          if (onError) {
            onError(data.error || 'Assessment processing failed');
          }
          break;
      }
    };

    // Poll immediately, then every 3 seconds
    pollStatus();
    const interval = setInterval(pollStatus, 3000);

    return () => clearInterval(interval);
  }, [jobId, isPolling, onComplete, onError, elapsedTime]);

  // Mock function for demo scenarios
  const getMockAssessmentStatus = async (jobId: string, elapsedTime: number) => {
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay

    if (jobId === 'demo-job-error') {
      if (elapsedTime > 5) {
        return {
          status: 'failed',
          progress: 0,
          error: 'Simulasi error dalam proses assessment',
          queuePosition: null,
          estimatedTimeRemaining: null
        };
      }
    }

    if (jobId === 'demo-job-1') {
      // Quick processing (2 seconds)
      if (elapsedTime < 2) {
        return {
          status: 'processing',
          progress: Math.min(90, elapsedTime * 45),
          queuePosition: null,
          estimatedTimeRemaining: 2 - elapsedTime
        };
      } else {
        return {
          status: 'completed',
          progress: 100,
          resultId: `result-demo-${Date.now()}`,
          result: { demo: true, title: 'Demo Assessment Result' }
        };
      }
    }

    if (jobId === 'demo-job-2') {
      // Normal processing (10 seconds)
      if (elapsedTime < 3) {
        return {
          status: 'queued',
          progress: 10,
          queuePosition: 3,
          estimatedTimeRemaining: 10 - elapsedTime
        };
      } else if (elapsedTime < 10) {
        return {
          status: 'processing',
          progress: Math.min(90, 10 + (elapsedTime - 3) * 11),
          queuePosition: null,
          estimatedTimeRemaining: 10 - elapsedTime
        };
      } else {
        return {
          status: 'completed',
          progress: 100,
          resultId: `result-demo-${Date.now()}`,
          result: { demo: true, title: 'Demo Assessment Result' }
        };
      }
    }

    if (jobId === 'demo-job-3') {
      // Long processing (30 seconds)
      if (elapsedTime < 5) {
        return {
          status: 'queued',
          progress: 5,
          queuePosition: Math.max(1, 8 - elapsedTime),
          estimatedTimeRemaining: 30 - elapsedTime
        };
      } else if (elapsedTime < 30) {
        return {
          status: 'processing',
          progress: Math.min(90, 5 + (elapsedTime - 5) * 3.4),
          queuePosition: null,
          estimatedTimeRemaining: 30 - elapsedTime
        };
      } else {
        return {
          status: 'completed',
          progress: 100,
          resultId: `result-demo-${Date.now()}`,
          result: { demo: true, title: 'Demo Assessment Result' }
        };
      }
    }

    // Default fallback
    return {
      status: 'processing',
      progress: Math.min(90, elapsedTime * 10),
      queuePosition: null,
      estimatedTimeRemaining: Math.max(0, 10 - elapsedTime)
    };
  };



  // Load queue info
  useEffect(() => {
    const loadQueueInfo = async () => {
      try {
        const info = await getAssessmentQueueInfo();
        setQueueInfo(info);
      } catch (err) {
        console.error('Error loading queue info:', err);
      }
    };

    loadQueueInfo();
    const interval = setInterval(loadQueueInfo, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Timer untuk elapsed time
  useEffect(() => {
    const timer = setInterval(() => {
      setElapsedTime(prev => prev + 1);
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const formatElapsedTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusDisplay = () => {
    switch (status) {
      case 'queued':
        return {
          icon: Clock,
          label: 'Dalam Antrian',
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          description: 'Assessment Anda sedang menunggu untuk diproses'
        };
      case 'processing':
        return {
          icon: Loader2,
          label: 'Sedang Diproses',
          color: 'text-orange-600',
          bgColor: 'bg-orange-50',
          description: 'AI sedang menganalisis hasil assessment Anda'
        };
      case 'completed':
        return {
          icon: CheckCircle,
          label: 'Selesai',
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          description: 'Analisis selesai! Hasil assessment siap ditampilkan'
        };
      case 'failed':
        return {
          icon: XCircle,
          label: 'Gagal',
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          description: 'Terjadi kesalahan dalam proses analisis'
        };
      default:
        return {
          icon: AlertCircle,
          label: 'Unknown',
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          description: 'Status tidak diketahui'
        };
    }
  };

  const statusDisplay = getStatusDisplay();

  const handleBackToDashboard = () => {
    router.push('/dashboard');
  };

  const handleRetry = () => {
    setError(null);
    setIsPolling(true);
    setStatus('queued');
    setProgress(0);
    setMessage('Memulai ulang proses analisis...');
  };

  return (
    <div className="min-h-screen bg-[#f5f7fb] flex items-center justify-center p-4">
      <div className="w-full max-w-2xl space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Memproses Hasil Assessment
          </h1>
          <p className="text-gray-600">
            Mohon tunggu sebentar, kami sedang menganalisis jawaban Anda dengan AI
          </p>
        </div>

        {/* Lottie Animation */}
        <div className="flex justify-center mb-8">
          <div className="w-80 h-80 bg-white rounded-lg shadow-sm flex items-center justify-center p-8">
            <LottieAnimation
              src="/loading/loading.json"
              width={300}
              height={300}
              loop={true}
              autoplay={true}
              className="rounded-lg"
            />
          </div>
        </div>

        {/* Status Card */}
        <Card className="bg-white border-gray-200/60 shadow-sm">
          <CardContent className="p-6">
            <div className="space-y-6">
              {/* Status Display */}
              <div className="flex items-center gap-4">
                <div className={`p-3 rounded-lg ${statusDisplay.bgColor}`}>
                  <statusDisplay.icon 
                    className={`w-6 h-6 ${statusDisplay.color} ${
                      status === 'processing' ? 'animate-spin' : ''
                    }`} 
                  />
                </div>
                <div className="flex-1">
                  <Badge 
                    variant={status === 'completed' ? 'default' : 'secondary'}
                    className={`${statusDisplay.color} ${statusDisplay.bgColor} border-0 mb-2`}
                  >
                    {statusDisplay.label}
                  </Badge>
                  <p className="text-sm text-gray-600">{statusDisplay.description}</p>
                  <p className="text-lg font-medium text-gray-900 mt-1">{message}</p>
                </div>
              </div>

              {/* Progress Bar */}
              {status !== 'failed' && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Progress</span>
                    <span>{progress}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              )}

              {/* Additional Info */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-100">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{formatElapsedTime(elapsedTime)}</div>
                  <div className="text-sm text-gray-500">Waktu Berlalu</div>
                </div>
                
                {queuePosition && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{queuePosition}</div>
                    <div className="text-sm text-gray-500">Posisi Antrian</div>
                  </div>
                )}
                
                {estimatedTime && (
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">{estimatedTime}</div>
                    <div className="text-sm text-gray-500">Estimasi Sisa</div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Queue Info */}
        {queueInfo && (
          <Card className="bg-white border-gray-200/60 shadow-sm">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Informasi Sistem</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    queueInfo.isServiceAvailable ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  <span className="text-sm">
                    {queueInfo.isServiceAvailable ? 'Sistem Online' : 'Mode Offline'}
                  </span>
                </div>
                
                <div className="flex items-center gap-3">
                  <Users className="w-4 h-4 text-gray-500" />
                  <span className="text-sm">{queueInfo.queueLength} dalam antrian</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <Clock className="w-4 h-4 text-gray-500" />
                  <span className="text-sm">Rata-rata: {queueInfo.averageProcessingTime}</span>
                </div>
                
                <div className="flex items-center gap-3">
                  <Activity className="w-4 h-4 text-gray-500" />
                  <span className="text-sm">Estimasi tunggu: {queueInfo.estimatedWaitTime}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error Display */}
        {error && (
          <Card className="bg-red-50 border-red-200 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-start gap-3">
                <XCircle className="w-5 h-5 text-red-600 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-red-900 mb-2">Terjadi Kesalahan</h3>
                  <p className="text-red-700 mb-4">{error}</p>
                  <div className="flex gap-3">
                    <Button onClick={handleRetry} variant="outline" size="sm">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Coba Lagi
                    </Button>
                    <Button onClick={handleBackToDashboard} variant="outline" size="sm">
                      <ArrowLeft className="w-4 h-4 mr-2" />
                      Kembali ke Dashboard
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        {!error && (
          <div className="flex justify-center">
            <Button 
              onClick={handleBackToDashboard} 
              variant="outline"
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Kembali ke Dashboard
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
