# Assessment Loading Page

## Overview
Halaman loading page untuk menampilkan proses assessment yang sedang dianalisis oleh AI. Halaman ini akan muncul setelah user menyelesaikan semua pertanyaan assessment dan menekan "Akhiri Test".

## Features

### 🎨 Visual Elements
- **Lottie Animation**: Menggunakan animasi dari `/public/loading/loading.json`
- **Progress Bar**: Menampilkan progress real-time dari 0-100%
- **Status Indicators**: Badge dan icon untuk setiap status (queued, processing, completed, failed)
- **Real-time Updates**: Informasi yang diupdate setiap 3 detik

### 📊 Information Display
- **Elapsed Time**: Timer yang menunjukkan berapa lama proses berjalan
- **Queue Position**: Posisi dalam antrian (jika ada)
- **Estimated Time**: Estimasi waktu tersisa
- **System Status**: Status sistem (online/offline)
- **Queue Info**: Informasi antrian dan rata-rata waktu proses

### 🔄 Status Flow
1. **Queued**: Assessment dalam antrian menunggu diproses
2. **Processing**: AI sedang menganalisis hasil assessment
3. **Completed**: <PERSON><PERSON><PERSON> se<PERSON>, redirect ke halaman hasil
4. **Failed**: Terjadi error, tampilkan pesan error dan opsi retry

## File Structure

```
├── components/assessment/
│   └── AssessmentLoadingPage.tsx          # Komponen utama loading page
├── components/ui/
│   └── LottieAnimation.tsx                # Wrapper untuk animasi Lottie
├── services/
│   └── assessment-submission.ts           # Service untuk submit dengan loading
├── app/
│   ├── assessment-loading/
│   │   └── page.tsx                       # Route halaman loading
│   └── assessment-loading-demo/
│       └── page.tsx                       # Demo halaman loading
└── public/loading/
    └── loading.json                       # Asset animasi Lottie
```

## Implementation

### 1. Assessment Submission Flow

```typescript
// Sebelum (langsung ke results)
Assessment → Submit → Results Page

// Sesudah (dengan loading page)
Assessment → Submit → Loading Page → Results Page
```

### 2. Service Integration

```typescript
// services/assessment-submission.ts
export async function submitAssessmentWithLoading(
  answers: Record<number, number | null>,
  userId?: string,
  onTokenBalanceUpdate?: () => Promise<void>
): Promise<AssessmentSubmissionResult>
```

**Return Values:**
- `jobId`: ID untuk polling status (Enhanced API)
- `resultId`: ID hasil langsung (Mock API)
- `useLoadingPage`: Boolean apakah perlu loading page
- `status`: 'processing' atau 'completed'

### 3. API Integration

**Enhanced API (Real-time processing):**
- Submit assessment → Get `jobId`
- Poll `/api/assessment/status/{jobId}` setiap 3 detik
- Redirect ke loading page dengan `jobId`

**Mock API (Instant results):**
- Submit assessment → Get `resultId` langsung
- Skip loading page, langsung ke results

### 4. Polling Configuration

```typescript
const POLLING_CONFIG = {
  INITIAL_DELAY: 3000,     // 3 seconds
  MAX_ATTEMPTS: 600,       // 30 minutes max
  TIMEOUT: 1800000,        // 30 minutes
}
```

## Usage

### 1. Normal Flow (dari AssessmentHeader)

```typescript
const submissionResult = await submitAssessmentWithLoading(
  answers, 
  user?.id, 
  refreshTokenBalance
);

if (submissionResult.useLoadingPage && submissionResult.jobId) {
  // Navigate to loading page
  router.push(`/assessment-loading?jobId=${submissionResult.jobId}`);
} else {
  // Navigate directly to results
  router.push(`/results/${submissionResult.resultId}`);
}
```

### 2. Direct Usage

```typescript
<AssessmentLoadingPage
  jobId="job-12345"
  onComplete={(resultId) => router.push(`/results/${resultId}`)}
  onError={(error) => console.error(error)}
/>
```

### 3. Demo Usage

Kunjungi `/assessment-loading-demo` untuk melihat berbagai skenario:
- Quick processing (2 detik)
- Normal processing (10 detik)
- Long processing (30 detik)
- Error scenario

## Components

### AssessmentLoadingPage

**Props:**
- `jobId: string` - ID job untuk polling
- `onComplete?: (resultId: string) => void` - Callback ketika selesai
- `onError?: (error: string) => void` - Callback ketika error

**Features:**
- Real-time status polling
- Progress tracking
- Queue information
- Error handling dengan retry
- Mock scenarios untuk demo

### LottieAnimation

**Props:**
- `src: string` - Path ke file JSON animasi
- `width?: number` - Lebar animasi (default: 200)
- `height?: number` - Tinggi animasi (default: 200)
- `loop?: boolean` - Loop animasi (default: true)
- `autoplay?: boolean` - Auto play (default: true)

**Features:**
- Dynamic import lottie-web
- Fallback ke spinner jika gagal load
- Responsive sizing

## Configuration

### 1. Routes Protection

Tambahkan ke `middleware.ts` dan `AuthGuard.tsx`:
```typescript
const protectedRoutes = [
  // ... existing routes
  '/assessment-loading',
  '/assessment-loading-demo'
];
```

### 2. Dependencies

```bash
npm install lottie-web
```

### 3. Asset Setup

Pastikan file animasi tersedia di:
```
public/loading/loading.json
```

## Error Handling

### 1. Polling Errors
- Network errors → Retry dengan exponential backoff
- Timeout → Redirect ke dashboard dengan pesan error
- API errors → Tampilkan error message dengan opsi retry

### 2. Animation Errors
- Lottie load failed → Fallback ke CSS spinner
- Invalid JSON → Fallback ke CSS spinner
- Network issues → Graceful degradation

### 3. Navigation Errors
- Failed redirect → Manual navigation dengan window.location
- Router issues → Debug logging dan fallback methods

## Testing

### 1. Demo Scenarios
```
/assessment-loading-demo
```

### 2. Manual Testing
1. Complete assessment
2. Click "Akhiri Test"
3. Verify loading page appears
4. Check status updates
5. Verify redirect to results

### 3. Error Testing
- Disconnect network during polling
- Invalid jobId
- API timeout scenarios

## Performance

### 1. Optimizations
- Lazy loading lottie-web
- Efficient polling with cleanup
- Memory leak prevention
- Proper component unmounting

### 2. Monitoring
- Console logging untuk debugging
- Error tracking
- Performance metrics
- User experience analytics

## Future Enhancements

### 1. Advanced Features
- WebSocket untuk real-time updates
- Push notifications
- Background processing
- Offline support

### 2. UI Improvements
- Multiple animation options
- Customizable themes
- Sound effects
- Haptic feedback (mobile)

### 3. Analytics
- Processing time tracking
- User engagement metrics
- Error rate monitoring
- Performance optimization

## Troubleshooting

### Common Issues

1. **Loading page tidak muncul**
   - Check Enhanced API availability
   - Verify jobId parameter
   - Check route protection

2. **Animasi tidak tampil**
   - Verify lottie-web installation
   - Check loading.json file path
   - Check browser console for errors

3. **Polling tidak berfungsi**
   - Check API endpoints
   - Verify authentication token
   - Check network connectivity

4. **Tidak redirect ke results**
   - Check onComplete callback
   - Verify resultId generation
   - Check navigation permissions

### Debug Tools

1. **Console Logging**
   ```typescript
   console.log('AssessmentLoadingPage: Status update:', data);
   ```

2. **Navigation Debug**
   ```typescript
   import { navigationDebugger } from '../../utils/navigation-debug';
   ```

3. **API Debug**
   ```typescript
   // Check Enhanced API availability
   const serviceAvailable = await isAssessmentServiceAvailable();
   ```
