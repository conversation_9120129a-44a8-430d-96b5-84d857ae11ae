'use client';

import React, { useEffect, useRef } from 'react';

interface LottieAnimationProps {
  src: string;
  width?: number;
  height?: number;
  loop?: boolean;
  autoplay?: boolean;
  className?: string;
}

export default function LottieAnimation({
  src,
  width = 200,
  height = 200,
  loop = true,
  autoplay = true,
  className = ''
}: LottieAnimationProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<any>(null);

  useEffect(() => {
    let lottie: any;
    
    const loadLottie = async () => {
      try {
        // Dynamically import lottie-web
        lottie = (await import('lottie-web')).default;
        
        if (containerRef.current && !animationRef.current) {
          // Load animation data
          const response = await fetch(src);
          const animationData = await response.json();
          
          // Create animation
          animationRef.current = lottie.loadAnimation({
            container: containerRef.current,
            renderer: 'svg',
            loop,
            autoplay,
            animationData
          });
        }
      } catch (error) {
        console.error('Error loading Lottie animation:', error);
        
        // Fallback: Show a simple loading spinner
        if (containerRef.current) {
          containerRef.current.innerHTML = `
            <div style="
              width: ${width}px; 
              height: ${height}px; 
              display: flex; 
              align-items: center; 
              justify-content: center;
              background: #f3f4f6;
              border-radius: 8px;
            ">
              <div style="
                width: 40px;
                height: 40px;
                border: 4px solid #e5e7eb;
                border-top: 4px solid #3b82f6;
                border-radius: 50%;
                animation: spin 1s linear infinite;
              "></div>
            </div>
            <style>
              @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
              }
            </style>
          `;
        }
      }
    };

    loadLottie();

    return () => {
      if (animationRef.current) {
        animationRef.current.destroy();
        animationRef.current = null;
      }
    };
  }, [src, loop, autoplay, width, height]);

  return (
    <div 
      ref={containerRef}
      className={className}
      style={{ width, height }}
    />
  );
}
