'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import AssessmentLoadingPage from '../../components/assessment/AssessmentLoadingPage';

export default function AssessmentLoadingDemoPage() {
  const router = useRouter();
  const [showDemo, setShowDemo] = useState(false);
  const [demoJobId, setDemoJobId] = useState('');

  const demoScenarios = [
    {
      id: 'demo-job-1',
      name: 'Quick Processing (2 seconds)',
      description: 'Simulasi proses cepat untuk testing'
    },
    {
      id: 'demo-job-2', 
      name: 'Normal Processing (10 seconds)',
      description: 'Simulasi proses normal dengan queue'
    },
    {
      id: 'demo-job-3',
      name: 'Long Processing (30 seconds)',
      description: '<PERSON><PERSON><PERSON><PERSON> proses lama dengan multiple status updates'
    },
    {
      id: 'demo-job-error',
      name: '<PERSON><PERSON><PERSON>',
      description: 'Simulasi error dalam proses assessment'
    }
  ];

  const handleStartDemo = (jobId: string) => {
    setDemoJobId(jobId);
    setShowDemo(true);
  };

  const handleDemoComplete = (resultId: string) => {
    console.log('Demo completed with resultId:', resultId);
    alert(`Demo selesai! ResultId: ${resultId}\n\nDalam implementasi nyata, user akan diarahkan ke halaman hasil.`);
    setShowDemo(false);
  };

  const handleDemoError = (error: string) => {
    console.error('Demo error:', error);
    alert(`Demo error: ${error}\n\nDalam implementasi nyata, user akan diarahkan kembali ke dashboard.`);
    setShowDemo(false);
  };

  const handleBackToDashboard = () => {
    router.push('/dashboard');
  };

  if (showDemo) {
    return (
      <AssessmentLoadingPage
        jobId={demoJobId}
        onComplete={handleDemoComplete}
        onError={handleDemoError}
      />
    );
  }

  return (
    <div className="min-h-screen bg-[#f5f7fb] p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Assessment Loading Page Demo
          </h1>
          <p className="text-gray-600">
            Demo halaman loading yang akan ditampilkan saat assessment sedang diproses
          </p>
        </div>

        {/* Info Card */}
        <Card className="bg-blue-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-blue-900 flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
              Tentang Loading Page
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-800">
            <div className="space-y-2">
              <p>• Halaman ini akan muncul setelah user menyelesaikan assessment dan menekan "Akhiri Test"</p>
              <p>• Menampilkan animasi Lottie dari file <code>/public/loading/loading.json</code></p>
              <p>• Melakukan polling status assessment setiap 3 detik</p>
              <p>• Menampilkan progress, posisi antrian, dan estimasi waktu</p>
              <p>• Otomatis redirect ke halaman hasil ketika selesai</p>
            </div>
          </CardContent>
        </Card>

        {/* Demo Scenarios */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {demoScenarios.map((scenario) => (
            <Card key={scenario.id} className="bg-white border-gray-200 hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{scenario.name}</CardTitle>
                  <Badge variant="outline">{scenario.id}</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 mb-4">{scenario.description}</p>
                <Button 
                  onClick={() => handleStartDemo(scenario.id)}
                  className="w-full"
                  variant={scenario.id.includes('error') ? 'destructive' : 'default'}
                >
                  Mulai Demo
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Implementation Notes */}
        <Card className="bg-gray-50 border-gray-200">
          <CardHeader>
            <CardTitle className="text-gray-900">Implementasi</CardTitle>
          </CardHeader>
          <CardContent className="text-gray-700">
            <div className="space-y-3">
              <div>
                <h4 className="font-semibold">Flow Normal:</h4>
                <p className="text-sm">Assessment → Submit → Loading Page → Results Page</p>
              </div>
              
              <div>
                <h4 className="font-semibold">Komponen Utama:</h4>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• <code>AssessmentLoadingPage.tsx</code> - Komponen utama loading page</li>
                  <li>• <code>LottieAnimation.tsx</code> - Wrapper untuk animasi Lottie</li>
                  <li>• <code>assessment-submission.ts</code> - Service untuk submit dengan loading</li>
                  <li>• <code>/assessment-loading</code> - Route untuk halaman loading</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold">API Integration:</h4>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Menggunakan Enhanced Assessment API jika tersedia</li>
                  <li>• Fallback ke Mock API jika service offline</li>
                  <li>• Polling status setiap 3 detik dengan exponential backoff</li>
                  <li>• Automatic timeout setelah 30 menit</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-center gap-4">
          <Button onClick={handleBackToDashboard} variant="outline">
            Kembali ke Dashboard
          </Button>
          <Button onClick={() => router.push('/assessment')} variant="default">
            Coba Assessment Asli
          </Button>
        </div>

        {/* Technical Info */}
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-4">
            <div className="text-sm text-yellow-800">
              <p className="font-semibold mb-2">Catatan Teknis:</p>
              <p>Demo ini menggunakan mock polling yang disimulasikan. Dalam implementasi nyata, 
              sistem akan melakukan polling ke API endpoint <code>/api/assessment/status/[jobId]</code> 
              untuk mendapatkan status real-time dari proses assessment.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
