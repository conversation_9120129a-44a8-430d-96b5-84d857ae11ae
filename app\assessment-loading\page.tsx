'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import AssessmentLoadingPage from '../../components/assessment/AssessmentLoadingPage';
import { useAuth } from '../../contexts/AuthContext';

export default function AssessmentLoadingPageRoute() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();
  const [jobId, setJobId] = useState<string | null>(null);

  useEffect(() => {
    // Get jobId from URL parameters
    const jobIdParam = searchParams.get('jobId');
    
    if (!jobIdParam) {
      console.error('AssessmentLoadingPage: No jobId provided');
      // Redirect to dashboard if no jobId
      router.push('/dashboard');
      return;
    }

    setJobId(jobIdParam);
  }, [searchParams, router]);

  const handleComplete = (resultId: string) => {
    console.log('AssessmentLoadingPage: Assessment completed, redirecting to results:', resultId);
    
    // Navigate to results page
    router.push(`/results/${resultId}`);
  };

  const handleError = (error: string) => {
    console.error('AssessmentLoadingPage: Assessment processing error:', error);
    
    // Show error and redirect to dashboard after delay
    setTimeout(() => {
      router.push('/dashboard');
    }, 5000);
  };

  // Show loading state while getting jobId
  if (!jobId) {
    return (
      <div className="min-h-screen bg-[#f5f7fb] flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Memuat halaman loading...</p>
        </div>
      </div>
    );
  }

  return (
    <AssessmentLoadingPage
      jobId={jobId}
      onComplete={handleComplete}
      onError={handleError}
    />
  );
}
